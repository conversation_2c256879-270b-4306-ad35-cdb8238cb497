<template>
  <div class="yz-base-container">
    <open-packup>
      <!-- 表单 -->
      <el-form
        ref="searchForm"
        class="yz-search-form"
        size="mini"
        :model="form"
        label-width="120px"
        @submit.native.prevent="search"
      >
        <el-form-item label="送礼单号" prop="id">
          <el-input v-model="form.id" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="商品id" prop="productId">
          <el-input v-model="form.productId" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="送礼状态" prop="status">
          <el-select
            v-model="form.status"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in statusOption"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="送礼人姓名" prop="consignorEmpName">
          <el-input v-model="form.consignorEmpName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="送礼人远智编号" prop="consignorEmpCode">
          <el-input v-model="form.consignorEmpCode" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="送礼人手机号码" prop="consignorEmpMobile">
          <el-input v-model="form.consignorEmpMobile" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="收礼人姓名" prop="consigneeUserName">
          <el-input v-model="form.consigneeUserName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="送礼人远智编号" prop="consigneeUserCode">
          <el-input v-model="form.consigneeUserCode" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="收礼人手机号码" prop="consigneeUserMobile">
          <el-input v-model="form.consigneeUserMobile" placeholder="请输入" />
        </el-form-item>

        <div class="search-reset-box">
          <el-button
            type="primary"
            icon="el-icon-search"
            native-type="submit"
            size="mini"
          >搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
        </div>
      </el-form>
    </open-packup>

    <!-- 按钮区 -->
    <div class="yz-table-btnbox">
      <el-button
        type="primary"
        size="small"
        @click="handleBatchImport"
      >批量导入送礼名单</el-button>
      <el-button
        type="primary"
        size="small"
        @click="handleWe"
      >富文本配置</el-button>
      <el-button
        type="primary"
        size="small"
        @click="exportData"
      >导出</el-button>
      <el-button type="primary" size="small" @click="add">新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
    >
      <el-table-column prop="id" label="送礼单号" align="center" />
      <el-table-column prop="productId" label="商品ID" align="center" />
      <el-table-column prop="productName" label="商品名称" align="center" />
      <el-table-column prop="productSpecName" label="规格名称" align="center" />
      <el-table-column prop="productAmount" label="件数" align="center" />
      <el-table-column prop="productMarketPrice" label="价格(元)" align="center" />
      <el-table-column prop="consignorEmpName" label="送礼人信息" align="left" header-align="center">
        <template slot-scope="scope">
          <p>远智编号: {{ scope.row.consignorEmpCode }}</p>
          <p>真实姓名: {{ scope.row.consignorEmpName }}</p>
          <p>手机号:{{ scope.row.consignorEmpMobile }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="consigneeUserName" label="收礼人" align="left" header-align="center">
        <template slot-scope="scope">
          <p>远智编号: {{ scope.row.consigneeUserCode }}</p>
          <p>真实姓名: {{ scope.row.consigneeUserName }}</p>
          <p>手机号:{{ scope.row.consigneeUserMobile }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="consigneeProvinceName" label="收礼地址" align="left" header-align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.consigneeMobile">
            <div>
              <div>
                <span>收货人:</span>
                <span>{{ scope.row.consigneeName }}</span>
              </div>
              <div>
                <span>手机号:</span>
                <span>{{ scope.row.consigneeMobile }}</span>
              </div>
            </div>
            <div>
              <span>收货地址:</span>
              <span>{{ scope.row.consigneeProvinceName }}</span>
              <span>{{ scope.row.consigneeCityName }}</span>
              <span>{{ scope.row.consigneeDistrictName }}</span>
              <span>{{ scope.row.consigneeStreetName }}</span>
              <span>{{ scope.row.consigneeAddress }}</span>
            </div>
          </template>
          <template v-else>
            <div style="text-align: center;">/</div>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="送礼状态" align="center">
        <template slot-scope="scope">
          {{ statusOption.find(item => item.value === scope.row.status).label }}
        </template>
      </el-table-column>
      <el-table-column prop="logisticsNo" label="物流单号" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.logisticsNo">{{ scope.row.logisticsNo }}</span>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column prop="actions" label="操作" align="center">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status === 'WAIT_OPEN'" type="text" @click="edit(scope.row)">编辑</el-button>
          <el-popover
            v-if="scope.row.status === 'WAIT_OPEN' || scope.row.status === 'WAIT_CONFIRM'"
            v-model="scope.row.visible"
            placement="left"
            title="请确认"
            width="200"
            trigger="click"
            style="display: block"
          >
            <p>取消赠送后，学员将无法收到礼物，请谨慎操作</p>
            <div style="text-align: right; margin-top: 10px">
              <el-button
                size="mini"
                type="text"
                @click="scope.row.visible = false"
              >取消</el-button>
              <el-button
                type="primary"
                size="mini"
                @click="handleCancel(scope.row)"
              >确定</el-button>
            </div>
            <el-button slot="reference" type="text">取消赠送</el-button>
          </el-popover>
          <el-popover
            v-if="scope.row.status === 'SUCCESS'"
            v-model="scope.row.visibleOrder"
            placement="left"
            title="请输入"
            width="220"
            trigger="click"
            style="display: block"
          >
            <p>
              <el-input
                :value="tempLogisticsNo"
                placeholder="请输入物流单号"
                @input="tempLogisticsNo = $event"
              />
            </p>
            <div style="text-align: right; margin-top: 10px">
              <el-button
                size="mini"
                type="text"
                @click="scope.row.visibleOrder = false"
              >取消</el-button>
              <el-button
                type="primary"
                size="mini"
                @click="handleOrder(scope.row)"
              >确定</el-button>
            </div>
            <el-button slot="reference" type="text" @click="showEditDialog(scope.row)">编辑物流单号</el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <wang-editor-dialog :visible.sync="weVisible" />
    <update-dialog :id="editId" :visible.sync="udVisible" :title="udTitle" />
    <!-- 批量导入送礼名单 -->
    <batch-import-dialog :visible.sync="batchImportVisible" @refresh="getTableList" />
  </div>
</template>

<script>
import wangEditorDialog from './wang-editor-dialog';
import updateDialog from './update-dialog';
import batchImportDialog from './batch-import-dialog';
import { exportExcel } from '@/utils';
import { statusOption } from './export.data';
import { api } from '@/api';

export default {
  components: {
    updateDialog,
    wangEditorDialog,
    batchImportDialog
  },
  filters: {
  },
  data() {
    return {
      weVisible: false,
      udVisible: false,
      udTitle: '新增',
      batchImportVisible: false,
      tableLoading: false,
      form: {
        id: '',
        status: '',
        productId: '',
        productName: '',
        consignorEmpName: '',
        consignorEmpCode: '',
        consignorEmpMobile: '',
        consigneeUserName: '',
        consigneeUserCode: '',
        consigneeUserMobile: ''
      },
      tableData: [],
      editId: null,
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      statusOption,
      tempLogisticsNo: '' // 添加临时变量存储物流单号
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    showEditDialog(row) {
      this.tempLogisticsNo = row.logisticsNo || ''; // 初始化临时变量
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    handleWe() {
      this.weVisible = true;
    },
    // 批量导入
    handleBatchImport() {
      this.batchImportVisible = true;
    },
    // 新增
    add() {
      this.editId = null;
      this.udTitle = '新增';
      this.udVisible = true;
    },
    // 编辑
    edit(row) {
      this.editId = row.id;
      this.udTitle = '编辑';
      this.udVisible = true;
    },
    getTableList() {
      this.tableLoading = true;
      const params = {
        ...this.form,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
      this.$post('giftGivingOrderPage', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
          this.tableLoading = false;
        }
      });
    },
    handleCancel(row) {
      const url = api['cancelGiftGivingOrder'].replace('{id}', row.id);
      this.$http.post(url).then((res) => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          row.visible = false;
          this.getTableList();
        }
      });
    },
    handleOrder(row) {
      const url = api['updateGiftGivingOrderLogisticsNo'].replace('{id}', row.id);
      this.$http.post(url, { logisticsNo: this.tempLogisticsNo }, { json: true }).then((res) => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          row.visibleOrder = false;
          this.getTableList();
        }
      });
    },
    // 导出函数
    exportData() {
      const data = this.form;
      exportExcel('giftGivingOrderExport', data);
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top: 16px;
}

.table-btnbox {
  margin: 20px 0 10px 0;
  .left {
    line-height: 33px;
  }
  .align-right {
    text-align: right;
  }
}
</style>
